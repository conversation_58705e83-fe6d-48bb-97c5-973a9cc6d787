using System;
using System.Windows.Controls;
using NetworkManagement.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace NetworkManagement.Views
{
    public partial class DevicesView : UserControl, IDisposable
    {
        private IServiceScope? _scope;
        private DevicesViewModel? _viewModel;

        public DevicesView()
        {
            InitializeComponent();
            Loaded += DevicesView_Loaded;
            Unloaded += DevicesView_Unloaded;
        }

        private void DevicesDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            e.Row.Header = (e.Row.GetIndex() + 1).ToString();
        }

        private async void DevicesView_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: بدء تحميل صفحة الأجهزة");

                if (_viewModel == null)
                {
                    _scope = App.CreateScope();
                    _viewModel = _scope.ServiceProvider.GetRequiredService<DevicesViewModel>();
                    DataContext = _viewModel;

                    System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: تم إنشاء DevicesViewModel");
                    System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: DataContext = {DataContext}");
                    System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: ViewModel = {_viewModel}");

                    await _viewModel.InitializeAsync();
                    System.Diagnostics.Debug.WriteLine("DevicesView_Loaded: تم تحميل الصفحة بنجاح");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: خطأ - {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"DevicesView_Loaded: تفاصيل الخطأ - {ex}");

                System.Windows.MessageBox.Show(
                    $"خطأ في تحميل صفحة الأجهزة:\n{ex.Message}\n\nتفاصيل إضافية:\n{ex.InnerException?.Message}",
                    "خطأ",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private void DevicesView_Unloaded(object sender, System.Windows.RoutedEventArgs e)
        {
            Dispose();
        }

        public void Dispose()
        {
            _viewModel = null;
            _scope?.Dispose();
            _scope = null;
        }
    }
}
